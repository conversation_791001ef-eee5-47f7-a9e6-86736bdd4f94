"""
Dependency injection for FastAPI services.

This module provides proper dependency injection for all services,
replacing the singleton pattern with FastAPI's dependency system.
"""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends, Request, HTTPException, status

from app.services.gemini_service import GeminiService
from app.services.typing_engine import TypingEngine
from app.services.user_service import UserService


@lru_cache()
def get_gemini_service() -> GeminiService:
    """
    Get Gemini service instance.
    
    Uses lru_cache to ensure singleton behavior within FastAPI's dependency system.
    
    Returns:
        GeminiService: Initialized Gemini service
    """
    return GeminiService()


@lru_cache()
def get_typing_engine() -> TypingEngine:
    """
    Get typing engine instance.
    
    Uses lru_cache to ensure singleton behavior within FastAPI's dependency system.
    
    Returns:
        TypingEngine: Initialized typing engine
    """
    return TypingEngine()


@lru_cache()
def get_user_service() -> UserService:
    """
    Get user service instance.
    
    Uses lru_cache to ensure singleton behavior within FastAPI's dependency system.
    
    Returns:
        UserService: Initialized user service
    """
    return UserService()


def get_graphiti_service(request: Request):
    """
    Get Graphiti service from app state.
    
    The Graphiti service is initialized during app startup and stored in app state
    because it requires async initialization.
    
    Args:
        request: FastAPI request object
        
    Returns:
        GraphitiService: Initialized Graphiti service
        
    Raises:
        HTTPException: If Graphiti service is not available
    """
    if not hasattr(request.app.state, 'graphiti_service'):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Graphiti service is not available"
        )
    return request.app.state.graphiti_service


# Type aliases for dependency injection
GeminiServiceDep = Annotated[GeminiService, Depends(get_gemini_service)]
TypingEngineDep = Annotated[TypingEngine, Depends(get_typing_engine)]
UserServiceDep = Annotated[UserService, Depends(get_user_service)]
GraphitiServiceDep = Annotated[object, Depends(get_graphiti_service)]  # Using object since GraphitiService is imported in main
