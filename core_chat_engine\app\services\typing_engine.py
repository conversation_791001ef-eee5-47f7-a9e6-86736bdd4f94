"""
Typing style engine for human-like message delivery.

This service transforms AI-generated responses into more human-like
message bursts with natural typing delays and patterns.
"""

import os
import random
import re
from typing import Dict, List, Optional, Any

from loguru import logger
from app.models.chat import MessageBurst


class TypingEngine:
    """
    Engine for creating human-like typing patterns and message bursts.

    This service makes <PERSON>'s responses feel more natural by:
    1. Breaking long responses into multiple shorter messages
    2. Adding realistic typing delays based on message length
    3. Adjusting typing speed based on emotional context
    4. Adding natural pauses between message bursts
    """

    def __init__(self):
        """Initialize typing engine with configuration."""
        # Get configuration from environment
        self.min_delay = float(os.getenv("TYPING_MIN_DELAY", "0.5"))
        self.max_delay = float(os.getenv("TYPING_MAX_DELAY", "2.0"))
        self.burst_size_min = int(os.getenv("TYPING_BURST_SIZE_MIN", "3"))
        self.burst_size_max = int(os.getenv("TYPING_BURST_SIZE_MAX", "8"))

        # Base typing speed (characters per second)
        self.base_typing_speed = 25.0

        # Speed variation range (percentage)
        self.speed_variation = 0.2

        # Maximum message length before splitting
        self.max_message_length = 150

        # Pause multiplier for different punctuation
        self.punctuation_pauses = {
            ".": 1.2,  # End of sentence
            "!": 1.3,  # Exclamation
            "?": 1.4,  # Question
            ",": 0.7,  # Comma
            ";": 0.9,  # Semicolon
            ":": 0.8,  # Colon
            "-": 0.6,  # Dash
            "...": 1.5,  # Ellipsis
        }

        # Emotion-based typing speed modifiers
        self.emotion_speed_modifiers = {
            "joy": 1.2,  # Faster when happy
            "sadness": 0.8,  # Slower when sad
            "anger": 1.3,  # Faster when angry
            "fear": 0.9,  # Slower when afraid
            "surprise": 1.1,  # Slightly faster when surprised
            "disgust": 0.9,  # Slower when disgusted
            "trust": 1.0,  # Normal speed when trusting
            "anticipation": 1.2,  # Faster when anticipating
            "neutral": 1.0,  # Normal speed when neutral
        }

        logger.info("Typing engine initialized")
    
    def format_response(
        self,
        response: str,
        emotion_detected: Optional[str] = None
    ) -> List[MessageBurst]:
        """
        Process an AI response into human-like message bursts.

        Args:
            response: Raw response from Gemini
            emotion_detected: Detected emotion for typing speed adjustment

        Returns:
            List of message bursts with typing delays
        """
        try:
            # Get emotional context for typing speed adjustment
            primary_emotion = emotion_detected or "neutral"

            # Split response into natural message chunks
            message_chunks = self._split_into_messages(response)

            # Calculate typing delays for each chunk
            message_bursts = []
            cumulative_delay = 0.0

            for i, chunk in enumerate(message_chunks):
                # Calculate typing delay based on length and emotion
                typing_duration = self._calculate_typing_delay(
                    message=chunk,
                    primary_emotion=primary_emotion
                )

                # Create message burst
                burst = MessageBurst(
                    text=chunk,
                    delay_before=cumulative_delay,
                    typing_duration=typing_duration
                )

                message_bursts.append(burst)

                # Add delay for next message (typing duration + pause)
                cumulative_delay += typing_duration + random.uniform(0.3, 0.8)

            logger.info(
                "Processed response with typing engine",
                extra={
                    "message_count": len(message_bursts),
                    "total_length": len(response),
                    "primary_emotion": primary_emotion,
                    "total_duration": cumulative_delay
                }
            )

            return message_bursts

        except Exception as e:
            logger.error(f"Typing engine error: {str(e)}", exc_info=True)

            # Fallback: return single message with minimal delay
            return [
                MessageBurst(
                    text=response,
                    delay_before=0.0,
                    typing_duration=1.0
                )
            ]
    
    def _split_into_messages(self, response: str) -> List[str]:
        """
        Split a response into natural message chunks.
        
        This function breaks a long response into smaller, more natural
        message chunks based on sentence boundaries and length limits.
        
        Args:
            response: Raw response to split
        
        Returns:
            List of message chunks
        """
        # Clean up the response
        response = response.strip()
        
        # If response is already short, return as is
        if len(response) <= self.max_message_length:
            return [response]
        
        # Split by paragraph breaks first
        paragraphs = re.split(r'\n\s*\n', response)
        
        message_chunks = []
        
        for paragraph in paragraphs:
            # Skip empty paragraphs
            if not paragraph.strip():
                continue
            
            # If paragraph is short enough, add as is
            if len(paragraph) <= self.max_message_length:
                message_chunks.append(paragraph.strip())
                continue
            
            # Split paragraph into sentences
            sentences = re.split(r'(?<=[.!?])\s+', paragraph)
            
            current_chunk = ""
            
            for sentence in sentences:
                # If adding this sentence would exceed max length, start a new chunk
                if len(current_chunk) + len(sentence) > self.max_message_length:
                    if current_chunk:
                        message_chunks.append(current_chunk.strip())
                    
                    # If sentence itself is too long, split it further
                    if len(sentence) > self.max_message_length:
                        # Split by phrases (commas, semicolons, etc.)
                        phrases = re.split(r'(?<=[,;:])\s+', sentence)
                        
                        current_chunk = ""
                        
                        for phrase in phrases:
                            if len(current_chunk) + len(phrase) > self.max_message_length:
                                if current_chunk:
                                    message_chunks.append(current_chunk.strip())
                                
                                # If phrase itself is too long, just add it as is
                                if len(phrase) > self.max_message_length:
                                    message_chunks.append(phrase.strip())
                                    current_chunk = ""
                                else:
                                    current_chunk = phrase
                            else:
                                if current_chunk:
                                    current_chunk += " " + phrase
                                else:
                                    current_chunk = phrase
                        
                        if current_chunk:
                            message_chunks.append(current_chunk.strip())
                            current_chunk = ""
                    else:
                        current_chunk = sentence
                else:
                    if current_chunk:
                        current_chunk += " " + sentence
                    else:
                        current_chunk = sentence
            
            if current_chunk:
                message_chunks.append(current_chunk.strip())
        
        # Ensure we have at least one chunk
        if not message_chunks:
            message_chunks = [response]
        
        return message_chunks
    
    def _calculate_typing_delay(
        self,
        message: str,
        primary_emotion: str
    ) -> float:
        """
        Calculate realistic typing delay for a message.

        Args:
            message: Message content
            primary_emotion: Primary emotion for speed adjustment

        Returns:
            Typing delay in seconds
        """
        # Base delay based on message length and typing speed
        base_delay = len(message) / self.base_typing_speed

        # Apply random variation
        variation = random.uniform(
            1.0 - self.speed_variation,
            1.0 + self.speed_variation
        )

        # Apply emotion-based speed modifier
        emotion_modifier = self.emotion_speed_modifiers.get(
            primary_emotion,
            1.0
        )

        # Calculate final delay
        typing_delay = base_delay * variation * emotion_modifier

        # Ensure minimum and maximum delay bounds
        typing_delay = max(typing_delay, self.min_delay)
        typing_delay = min(typing_delay, self.max_delay)

        # Add extra delay for messages ending with certain punctuation
        for punct, multiplier in self.punctuation_pauses.items():
            if message.strip().endswith(punct):
                typing_delay *= multiplier
                break

        return typing_delay
    
    def _adjust_for_emotion(self, message: str, emotion: str) -> str:
        """
        Adjust message style based on emotion (optional enhancement).

        This could add subtle emotional markers like:
        - Exclamation points for joy
        - Ellipses for sadness
        - Capitalization for anger

        Args:
            message: Original message
            emotion: Emotion to adjust for

        Returns:
            Adjusted message
        """
        # This is a placeholder for potential future enhancement
        return message
