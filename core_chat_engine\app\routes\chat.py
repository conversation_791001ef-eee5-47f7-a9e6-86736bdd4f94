"""
Main chat routes for <PERSON>'s conversational interface.

This module handles the core chat functionality, including message processing,
Gemini integration, Graphiti memory operations, and the typing style engine
that makes <PERSON> feel more human-like.
"""

import asyncio
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from loguru import logger

from app.models.chat import ChatRequest, ChatResponse, MessageBurst
from app.dependencies import GeminiServiceDep, GraphitiServiceDep, TypingEngineDep
from app.utils.rate_limiting import limiter, chat_rate_limit
from app.utils.input_validation import ContentFilter


# Initialize router
router = APIRouter()


# Import authentication dependency
from app.routes.auth import get_current_user


@router.post(
    "/",
    response_model=ChatResponse,
    summary="Send chat message",
    description="Send a message to <PERSON> and receive her response",
)
@chat_rate_limit()
async def chat(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    app_request: Request,
    current_user: str = Depends(get_current_user),
    gemini_service: GeminiServiceDep,
    graphiti_service: GraphitiServiceDep,
    typing_engine: TypingEngineDep,
):
    """
    Process a chat message and return <PERSON>'s response.

    This is the core conversational endpoint that orchestrates the cognitive loop:
    1. Retrieves relevant context from Graphiti
    2. Builds comprehensive prompt for Gemini
    3. Generates response using Gemini
    4. Applies typing style engine for human-like delivery
    5. Stores episode in memory (background task)

    Args:
        request: Chat message request
        background_tasks: FastAPI background tasks
        app_request: FastAPI request object
        current_user: Authenticated username

    Returns:
        ChatResponse: Chat response with message bursts
    """
    start_time = time.time()
    conversation_id = request.conversation_id or str(uuid.uuid4())

    try:
        # Validate and sanitize input message
        validated_message = ContentFilter.validate_message_content(request.message)

        # Services are injected via dependencies
        # Use current_user as user_id for memory isolation
        user_id = current_user

        # Step 1: Retrieve relevant context from memory
        logger.info(f"Searching for relevant context for user: {user_id}")
        context = await graphiti_service.search_relevant_context(
            group_id=user_id,  # Use user_id as group_id for memory isolation
            query=validated_message,
            limit=10
        )

        # Step 2: Build comprehensive prompt for Gemini
        prompt = gemini_service._build_prompt(context, validated_message)

        # Step 3: Generate response using Gemini
        if request.use_grounding:
            # Use grounded response for factual queries
            gemini_response = await gemini_service.get_grounded_response(prompt)
            response_text = gemini_response["text"]
            grounding_metadata = gemini_response.get("grounding_metadata")
        else:
            # Use regular emotional/conversational response
            response_text = await gemini_service.get_response(prompt)
            grounding_metadata = None

        # Step 4: Apply typing engine for human-like delivery
        message_bursts = typing_engine.format_response(response_text)

        # Step 5: Prepare episode data for background storage
        episode_content = {
            "user_message": validated_message,
            "assistant_response": response_text,
            "conversation_id": conversation_id,
            "context_used": context,
            "timestamp": datetime.utcnow().isoformat(),
            "use_grounding": request.use_grounding
        }

        # Add episode to memory in background
        background_tasks.add_task(
            graphiti_service.add_episode,
            user_id,  # group_id
            episode_content
        )

        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000

        # Build response
        response = ChatResponse(
            success=True,
            message="Response generated successfully",
            request_id=getattr(app_request.state, "request_id", None),
            response_text=response_text,
            message_bursts=message_bursts,
            conversation_id=conversation_id,
            response_type="grounded" if request.use_grounding else "emotional",
            grounding_metadata=grounding_metadata,
            memory_context_used=len(context.get("memories", [])) > 0,
            processing_time_ms=processing_time_ms
        )

        logger.info(
            "Chat message processed successfully",
            extra={
                "conversation_id": conversation_id,
                "user_id": user_id,
                "message_length": len(validated_message),
                "response_length": len(response_text),
                "message_bursts": len(message_bursts),
                "memories_used": len(context.get("memories", [])),
                "processing_time_ms": round(processing_time_ms, 2)
            }
        )

        return response

    except Exception as e:
        processing_time_ms = (time.time() - start_time) * 1000
        logger.error(
            f"Chat processing error: {str(e)}",
            extra={
                "user_id": user_id,
                "conversation_id": conversation_id,
                "processing_time_ms": round(processing_time_ms, 2)
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process message"
        )


@router.get(
    "/status",
    summary="Chat service status",
    description="Check if the chat service is ready to process messages",
)
async def chat_status(
    app_request: Request,
    gemini_service: GeminiServiceDep,
    graphiti_service: GraphitiServiceDep,
    typing_engine: TypingEngineDep,
):
    """
    Check the status of the chat service and its dependencies.

    Returns:
        Dict with service status information
    """
    try:
        # Services are injected and available if we reach this point
        return {
            "status": "ready",
            "services": {
                "graphiti": "available",
                "gemini": "available",
                "typing_engine": "available"
            },
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Chat status check failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Chat service is not ready"
        )
