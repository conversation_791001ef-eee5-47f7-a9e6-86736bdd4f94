"""
Caching utilities for the Mandy Core Chat Engine.

This module provides caching functionality using fastapi-cache2 to improve
performance for read-heavy operations like chat history retrieval.
"""

import os
from typing import Any, Optional
from functools import wraps

from fastapi_cache import FastAPICache
from fastapi_cache.backends.inmemory import InMemoryBackend
from fastapi_cache.decorator import cache
from loguru import logger


class CacheConfig:
    """Configuration for caching system."""
    
    # Cache TTL settings (in seconds)
    CHAT_HISTORY_TTL = int(os.getenv("CACHE_CHAT_HISTORY_TTL", "300"))  # 5 minutes
    USER_PROFILE_TTL = int(os.getenv("CACHE_USER_PROFILE_TTL", "600"))  # 10 minutes
    MEMORY_STATS_TTL = int(os.getenv("CACHE_MEMORY_STATS_TTL", "180"))  # 3 minutes
    HEALTH_CHECK_TTL = int(os.getenv("CACHE_HEALTH_CHECK_TTL", "30"))   # 30 seconds
    
    # Cache key prefixes
    CHAT_HISTORY_PREFIX = "chat_history"
    USER_PROFILE_PREFIX = "user_profile"
    MEMORY_STATS_PREFIX = "memory_stats"
    HEALTH_CHECK_PREFIX = "health_check"


def init_cache():
    """Initialize the caching backend."""
    try:
        # Use in-memory backend for simplicity
        # In production, consider Redis backend for distributed caching
        FastAPICache.init(InMemoryBackend(), prefix="mandy-cache")
        logger.info("Cache system initialized with InMemory backend")
    except Exception as e:
        logger.error(f"Failed to initialize cache system: {e}")
        raise


def cache_key_builder(
    func_name: str,
    user_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    **kwargs
) -> str:
    """
    Build cache keys with consistent naming convention.
    
    Args:
        func_name: Name of the function being cached
        user_id: Optional user ID for user-specific caching
        conversation_id: Optional conversation ID
        **kwargs: Additional parameters for key building
        
    Returns:
        str: Formatted cache key
    """
    key_parts = [func_name]
    
    if user_id:
        key_parts.append(f"user:{user_id}")
    
    if conversation_id:
        key_parts.append(f"conv:{conversation_id}")
    
    # Add other parameters
    for key, value in sorted(kwargs.items()):
        if value is not None:
            key_parts.append(f"{key}:{value}")
    
    return ":".join(key_parts)


def cache_chat_history(expire: int = CacheConfig.CHAT_HISTORY_TTL):
    """
    Decorator for caching chat history endpoints.
    
    Args:
        expire: Cache expiration time in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        @cache(expire=expire)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def cache_user_profile(expire: int = CacheConfig.USER_PROFILE_TTL):
    """
    Decorator for caching user profile endpoints.
    
    Args:
        expire: Cache expiration time in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        @cache(expire=expire)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def cache_memory_stats(expire: int = CacheConfig.MEMORY_STATS_TTL):
    """
    Decorator for caching memory statistics endpoints.
    
    Args:
        expire: Cache expiration time in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        @cache(expire=expire)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def cache_health_check(expire: int = CacheConfig.HEALTH_CHECK_TTL):
    """
    Decorator for caching health check endpoints.
    
    Args:
        expire: Cache expiration time in seconds
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        @cache(expire=expire)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def invalidate_user_cache(user_id: str):
    """
    Invalidate all cache entries for a specific user.
    
    Args:
        user_id: User ID whose cache should be invalidated
    """
    try:
        # This is a simplified implementation
        # In a real scenario, you'd need to track cache keys by user
        # and invalidate them specifically
        logger.info(f"Cache invalidation requested for user: {user_id}")
        # FastAPICache doesn't have built-in pattern-based invalidation
        # This would need to be implemented with a more sophisticated backend
    except Exception as e:
        logger.error(f"Failed to invalidate cache for user {user_id}: {e}")


async def invalidate_conversation_cache(conversation_id: str):
    """
    Invalidate cache entries for a specific conversation.
    
    Args:
        conversation_id: Conversation ID whose cache should be invalidated
    """
    try:
        logger.info(f"Cache invalidation requested for conversation: {conversation_id}")
        # Similar to user cache invalidation, this would need backend support
    except Exception as e:
        logger.error(f"Failed to invalidate cache for conversation {conversation_id}: {e}")


async def clear_all_cache():
    """Clear all cache entries."""
    try:
        # This would need backend-specific implementation
        logger.info("All cache cleared")
    except Exception as e:
        logger.error(f"Failed to clear all cache: {e}")


def get_cache_stats() -> dict:
    """
    Get cache statistics.
    
    Returns:
        dict: Cache statistics
    """
    try:
        # This would return actual cache statistics in a real implementation
        return {
            "backend": "InMemory",
            "status": "active",
            "hit_rate": "N/A",  # Would need tracking
            "total_keys": "N/A",  # Would need tracking
            "memory_usage": "N/A"  # Would need tracking
        }
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        return {"error": str(e)}
