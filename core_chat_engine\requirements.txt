# FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Data Models and Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# HTTP Client
httpx==0.25.2

# Logging
loguru==0.7.2

# Google Gemini AI
google-generativeai==0.3.2

# Graphiti Memory Framework (from GitHub)
git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core

# Neo4j Database Driver
neo4j==5.15.0

# Background Tasks
celery==5.3.4

# Async Support
asyncio-mqtt==0.13.0

# Utilities
python-dateutil==2.8.2
typing-extensions==4.8.0

# Additional Production Dependencies
gunicorn==21.2.0
orjson==3.9.10
psutil==5.9.6
cryptography==41.0.8
PyJWT==2.8.0
