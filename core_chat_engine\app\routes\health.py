"""
Health check endpoints for monitoring service status.

These endpoints provide health status information for the service
and its dependencies, used for monitoring and alerting.
"""

import time
import os
import psutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status, Request
from loguru import logger

from app.models.common import HealthResponse
from app.dependencies import GeminiServiceDep, GraphitiServiceDep, TypingEngineDep
from app.utils.rate_limiting import health_rate_limit


# Initialize router
router = APIRouter()

# Service start time for uptime calculation
SERVICE_START_TIME = time.time()


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Service health check",
    description="Check the health status of the service and its dependencies",
)
@health_rate_limit()
async def health_check(
    request: Request,
    gemini_service: GeminiServiceDep,
    graphiti_service: GraphitiServiceDep,
    typing_engine: TypingEngineDep,
):
    """
    Perform a comprehensive health check of the service and its dependencies.

    Returns:
        HealthResponse: Health status information
    """
    current_time = datetime.utcnow()
    uptime_seconds = time.time() - SERVICE_START_TIME

    # Initialize health status
    overall_status = "healthy"
    services = {}

    # Check Graphiti service
    try:
        # Services are injected, so if we reach here they're available
        services["graphiti"] = {
            "status": "healthy",
            "message": "Service available",
            "response_time_ms": 0  # Placeholder for actual response time
        }
    except Exception as e:
        services["graphiti"] = {
            "status": "unhealthy",
            "message": f"Service error: {str(e)}",
            "response_time_ms": None
        }
        overall_status = "degraded"

    # Check Gemini service
    try:
        services["gemini"] = {
            "status": "healthy",
            "message": "Service available",
            "response_time_ms": 0
        }
    except Exception as e:
        services["gemini"] = {
            "status": "unhealthy",
            "message": f"Service error: {str(e)}",
            "response_time_ms": None
        }
        overall_status = "degraded"

    # Check Typing Engine
    try:
        services["typing_engine"] = {
            "status": "healthy",
            "message": "Service available",
            "response_time_ms": 0
        }
    except Exception as e:
        services["typing_engine"] = {
            "status": "unhealthy",
            "message": f"Service error: {str(e)}",
            "response_time_ms": None
        }
        overall_status = "degraded"

    # Get system metrics
    try:
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        disk = psutil.disk_usage('/')

        system_metrics = {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "memory_available_mb": memory.available // (1024 * 1024),
            "disk_usage_percent": disk.percent,
            "disk_free_gb": disk.free // (1024 * 1024 * 1024)
        }
    except Exception as e:
        logger.warning(f"Failed to get system metrics: {e}")
        system_metrics = {"error": "Unable to retrieve system metrics"}

    # Determine overall health
    if overall_status == "healthy":
        status_code = status.HTTP_200_OK
    else:
        status_code = status.HTTP_503_SERVICE_UNAVAILABLE

    return HealthResponse(
        status=overall_status,
        timestamp=current_time,
        uptime_seconds=int(uptime_seconds),
        version=os.getenv("APP_VERSION", "1.0.0"),
        environment=os.getenv("ENVIRONMENT", "development"),
        services=services,
        system_metrics=system_metrics
    )
            services["graphiti"] = {
                "status": "healthy",
                "description": "Graphiti memory service"
            }
        else:
            services["graphiti"] = {
                "status": "unhealthy",
                "description": "Graphiti service not initialized"
            }
            status = "degraded"
    except Exception as e:
        logger.error(f"Graphiti health check failed: {str(e)}")
        services["graphiti"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        status = "degraded"

    # Check Gemini API configuration
    try:
        google_api_key = os.getenv("GOOGLE_API_KEY")
        if google_api_key:
            services["gemini"] = {
                "status": "healthy",
                "description": "Google Gemini API configured"
            }
        else:
            services["gemini"] = {
                "status": "unhealthy",
                "description": "Google Gemini API key not configured"
            }
            status = "degraded"
    except Exception as e:
        logger.error(f"Gemini health check failed: {str(e)}")
        services["gemini"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        status = "degraded"

    # Check Neo4j configuration
    try:
        neo4j_uri = os.getenv("NEO4J_URI")
        if neo4j_uri:
            services["neo4j"] = {
                "status": "healthy",
                "description": "Neo4j database configured"
            }
        else:
            services["neo4j"] = {
                "status": "unhealthy",
                "description": "Neo4j URI not configured"
            }
            status = "degraded"
    except Exception as e:
        services["neo4j"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        status = "degraded"

    # Create health response
    health_response = HealthResponse(
        status=status,
        version="1.0.0",
        timestamp=current_time.isoformat(),
        services=services,
        uptime_seconds=uptime_seconds
    )

    # Log health status
    logger.info(f"Health check completed: {status}", extra={
        "uptime_seconds": uptime_seconds,
        "services": services
    })

    return health_response


@router.get(
    "/readiness",
    status_code=status.HTTP_200_OK,
    summary="Readiness probe",
    description="Check if the service is ready to accept requests",
)
async def readiness_probe(request: Request):
    """
    Check if the service is ready to accept requests.

    This endpoint is used by Kubernetes or other orchestration systems
    to determine if the service is ready to receive traffic.

    Returns:
        dict: Simple status message
    """
    # Check critical dependencies
    try:
        # Check if Graphiti service is initialized
        if not hasattr(request.app.state, 'graphiti_service'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service is not ready: Graphiti service not initialized",
            )

        # Check if required environment variables are set
        required_env_vars = ["GOOGLE_API_KEY", "NEO4J_URI"]
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        if missing_vars:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Service is not ready: Missing environment variables: {', '.join(missing_vars)}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service is not ready: {str(e)}",
        )

    return {"status": "ready"}


@router.get(
    "/liveness",
    status_code=status.HTTP_200_OK,
    summary="Liveness probe",
    description="Check if the service is alive",
)
async def liveness_probe():
    """
    Check if the service is alive.

    This endpoint is used by Kubernetes or other orchestration systems
    to determine if the service is still running.

    Returns:
        dict: Simple status message
    """
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
