# Core dependencies for Mandy AI Companion
# Production-grade, microservice-driven, emotionally intelligent AI companion

# ============================================================================
# CORE FRAMEWORK DEPENDENCIES
# ============================================================================

# FastAPI and ASGI server
fastapi==0.115.4
uvicorn[standard]==0.32.1
pydantic==2.10.3
pydantic-settings==2.6.1

# Async HTTP client for inter-service communication
httpx[test]==0.28.1
aiohttp==3.11.10

# ============================================================================
# AI & ML DEPENDENCIES
# ============================================================================

# Google Gemini API
google-generativeai==0.8.3

# Graphiti framework for temporal knowledge graphs
git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core

# Neo4j driver for graph database
neo4j==5.27.0

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================

# JWT token handling
PyJWT==2.10.1
passlib[bcrypt]==1.7.4
python-multipart==0.0.12

# Rate limiting
slowapi==0.1.9

# Cryptography for secure operations
cryptography>=44.0.0

# ============================================================================
# WEB UI & INTERFACE
# ============================================================================

# Gradio for web interface
gradio==5.9.1

# ============================================================================
# BACKGROUND PROCESSING
# ============================================================================

# Scheduler for background tasks
APScheduler==3.11.0

# Redis for task queuing (optional)
redis==5.2.1
celery==5.4.0

# ============================================================================
# EXTERNAL API INTEGRATIONS
# ============================================================================

# News API client
newsapi-python==0.2.7

# Reddit API client
praw==7.7.1

# RSS feed parsing
feedparser==6.0.10

# Web scraping utilities
beautifulsoup4==4.12.3
requests==2.32.3

# ============================================================================
# LOGGING & MONITORING
# ============================================================================

# Structured logging
loguru==0.7.2

# Performance monitoring
psutil==6.1.0

# Prometheus metrics
starlette-prometheus==0.10.0

# Caching
fastapi-cache2==0.2.2

# ============================================================================
# DATA PROCESSING & UTILITIES
# ============================================================================

# Date/time handling
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Environment variable loading
python-dotenv==1.0.0

# UUID utilities
uuid==1.30

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================

# Testing framework
pytest==8.3.4
pytest-asyncio==0.24.0
pytest-cov==6.0.0
pytest-mock==3.14.0

# HTTP testing (included in httpx[test] above)

# Linting and formatting
black==24.10.0
isort==5.13.2
flake8==7.1.1
mypy==1.13.0

# Type hints
types-requests==2.32.0.20241016
types-redis==4.6.0.20241004

# ============================================================================
# DEPLOYMENT & CONTAINERIZATION
# ============================================================================

# WSGI/ASGI servers
gunicorn==21.2.0

# Health checks
healthcheck==1.3.3

# ============================================================================
# OPTIONAL DEPENDENCIES
# ============================================================================

# Voice synthesis (experimental)
# pyttsx3==2.90

# Image processing (experimental)
# Pillow==10.1.0
# opencv-python==4.8.1.78

# Natural language processing
# spacy==3.7.2
# transformers==4.35.2

# ============================================================================
# PLATFORM-SPECIFIC DEPENDENCIES
# ============================================================================

# For Windows compatibility
# pywin32==306; sys_platform == "win32"

# For Linux/Unix systems
# python-magic==0.4.27; sys_platform != "win32"
