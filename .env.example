# Core Application Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Google Gemini API Configuration
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-1.5-pro-latest
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=2048

# Security Configuration
SECRET_KEY=your-super-secret-jwt-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:7860

# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password_here
NEO4J_DATABASE=neo4j

# Graphiti Configuration
GRAPHITI_GROUP_ID=mandy_default_group

# Authentication Configuration (Demo purposes)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# UI Configuration
UI_HOST=0.0.0.0
UI_PORT=7860
CORE_CHAT_ENGINE_URL=http://core-chat-engine:8000

# Demo User Credentials
DEMO_USERNAME=user
DEMO_PASSWORD=password

# Typing Engine Configuration
TYPING_MIN_DELAY=0.5
TYPING_MAX_DELAY=2.0
TYPING_BURST_SIZE_MIN=3
TYPING_BURST_SIZE_MAX=8
# =============================================================================
# MANDY - EMOTIONALLY INTELLIGENT AI COMPANION
# Environment Configuration Template
# =============================================================================

# -----------------------------------------------------------------------------
# CORE API KEYS & AUTHENTICATION
# -----------------------------------------------------------------------------

# Google Gemini API Key (Required)
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=demo_key_for_testing

# JWT Secret Key for Authentication (Required)
# Generate with: openssl rand -hex 32
SECRET_KEY=demo_secret_key_for_development_only_change_in_production

# Default Admin Credentials (Change in Production!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change_me_in_production

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION
# -----------------------------------------------------------------------------

# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j

# Alternative: Neo4j AuraDB (Cloud)
# NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
# NEO4J_USER=neo4j
# NEO4J_PASSWORD=your_aura_password

# -----------------------------------------------------------------------------
# SERVICE CONFIGURATION
# -----------------------------------------------------------------------------

# Environment (development, staging, production)
ENVIRONMENT=development

# Service URLs (for inter-service communication)
CORE_API_URL=http://localhost:8000
UI_URL=http://localhost:7860

# Service Ports
CORE_API_PORT=8000
UI_PORT=7860
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687

# -----------------------------------------------------------------------------
# EXTERNAL API INTEGRATIONS
# -----------------------------------------------------------------------------

# News API (for content fetching)
# Get from: https://newsapi.org/
NEWS_API_KEY=your_news_api_key_here

# Reddit API (for content fetching)
# Get from: https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=Mandy/1.0

# arXiv API (no key required, but rate limiting applies)
ARXIV_API_BASE_URL=http://export.arxiv.org/api/query

# -----------------------------------------------------------------------------
# WORKER CONFIGURATION
# -----------------------------------------------------------------------------

# Content Fetcher Settings
CONTENT_FETCH_INTERVAL=3600  # seconds (1 hour)
MAX_ARTICLES_PER_FETCH=10
CONTENT_RETENTION_DAYS=30

# Reflection Engine Settings
REFLECTION_INTERVAL=1800  # seconds (30 minutes)
TRUST_SCORE_DECAY_RATE=0.01
EMOTION_ANALYSIS_THRESHOLD=0.7

# -----------------------------------------------------------------------------
# LOGGING & MONITORING
# -----------------------------------------------------------------------------

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log Format (json, text)
LOG_FORMAT=json

# Enable/Disable specific loggers
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_SECURITY_LOGGING=true

# -----------------------------------------------------------------------------
# SECURITY CONFIGURATION
# -----------------------------------------------------------------------------

# CORS Settings
CORS_ORIGINS=["http://localhost:7860", "http://localhost:3000"]
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
MAX_CONCURRENT_SESSIONS=5

# -----------------------------------------------------------------------------
# PERFORMANCE & SCALING
# -----------------------------------------------------------------------------

# Gemini Model Configuration
GEMINI_MODEL=gemini-1.5-pro
GEMINI_SMALL_MODEL=gemini-1.5-flash
GEMINI_MAX_TOKENS=8192
GEMINI_TEMPERATURE=0.7

# Graphiti Configuration
GRAPHITI_EMBEDDING_MODEL=text-embedding-004
GRAPHITI_EMBEDDING_DIMENSION=768
GRAPHITI_MAX_CONTEXT_LENGTH=4000

# Worker Pool Settings
MAX_WORKER_THREADS=4
WORKER_QUEUE_SIZE=100

# -----------------------------------------------------------------------------
# DEPLOYMENT CONFIGURATION
# -----------------------------------------------------------------------------

# Docker Configuration
COMPOSE_PROJECT_NAME=mandy
DOCKER_BUILDKIT=1

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# -----------------------------------------------------------------------------
# DEVELOPMENT SETTINGS
# -----------------------------------------------------------------------------

# Development Mode
DEBUG=true
RELOAD=true

# Testing Configuration
TEST_DATABASE_URL=bolt://localhost:7687
TEST_DATABASE_NAME=mandy_test

# Mock External APIs (for testing)
MOCK_EXTERNAL_APIS=false

# -----------------------------------------------------------------------------
# PRODUCTION OVERRIDES
# -----------------------------------------------------------------------------

# Uncomment and modify for production deployment

# ENVIRONMENT=production
# DEBUG=false
# RELOAD=false
# LOG_LEVEL=WARNING
# CORS_ORIGINS=["https://your-domain.com"]

# Production Database (if different)
# NEO4J_URI=neo4j+s://production-instance.databases.neo4j.io
# NEO4J_PASSWORD=secure_production_password

# Production Security
# SECRET_KEY=very_long_and_secure_production_secret_key
# ADMIN_PASSWORD=secure_admin_password

# -----------------------------------------------------------------------------
# PLATFORM-SPECIFIC CONFIGURATION
# -----------------------------------------------------------------------------

# Render.com
# PORT=10000

# Railway
# RAILWAY_STATIC_URL=https://your-app.railway.app

# Hugging Face Spaces
# SPACE_ID=your-username/mandy

# -----------------------------------------------------------------------------
# OPTIONAL FEATURES
# -----------------------------------------------------------------------------

# Enable/Disable Features
ENABLE_CONTENT_FETCHING=true
ENABLE_REFLECTION_ENGINE=true
ENABLE_EMOTION_ANALYSIS=true
ENABLE_TRUST_MODELING=true

# Experimental Features
ENABLE_VOICE_SYNTHESIS=false
ENABLE_IMAGE_ANALYSIS=false
ENABLE_MULTIMODAL_CHAT=false
