"""
Standardized error handling for the Mandy Core Chat Engine.

This module provides comprehensive error handling with consistent JSON responses,
proper logging, and appropriate HTTP status codes.
"""

import traceback
from typing import Any, Dict, Optional, Union

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from loguru import logger
from pydantic import ValidationError
from slowapi.errors import RateLimitExceeded


class MandyException(Exception):
    """Base exception class for Mandy-specific errors."""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class ServiceUnavailableError(MandyException):
    """Raised when a required service is unavailable."""
    
    def __init__(self, service_name: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Service '{service_name}' is currently unavailable",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="SERVICE_UNAVAILABLE",
            details=details or {"service": service_name}
        )


class AuthenticationError(MandyException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="AUTHENTICATION_FAILED",
            details=details
        )


class ValidationError(MandyException):
    """Raised when input validation fails."""
    
    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if field:
            error_details["field"] = field
            
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="VALIDATION_ERROR",
            details=error_details
        )


def create_error_response(
    request: Request,
    status_code: int,
    message: str,
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    include_traceback: bool = False
) -> JSONResponse:
    """
    Create a standardized error response.
    
    Args:
        request: FastAPI request object
        status_code: HTTP status code
        message: Error message
        error_code: Optional error code for categorization
        details: Optional additional error details
        include_traceback: Whether to include traceback in response
        
    Returns:
        JSONResponse: Standardized error response
    """
    error_response = {
        "success": False,
        "error": {
            "code": error_code or "UNKNOWN_ERROR",
            "message": message,
            "status_code": status_code
        },
        "request_id": getattr(request.state, "request_id", None),
        "timestamp": logger._core.now().isoformat()
    }
    
    if details:
        error_response["error"]["details"] = details
    
    if include_traceback and logger._core.min_level <= 10:  # DEBUG level
        error_response["error"]["traceback"] = traceback.format_exc()
    
    return JSONResponse(
        status_code=status_code,
        content=error_response,
        headers={"X-Request-ID": str(getattr(request.state, "request_id", ""))}
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTPException with standardized response format.
    
    Args:
        request: FastAPI request object
        exc: HTTPException instance
        
    Returns:
        JSONResponse: Standardized error response
    """
    logger.warning(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
            "request_id": getattr(request.state, "request_id", None)
        }
    )
    
    return create_error_response(
        request=request,
        status_code=exc.status_code,
        message=str(exc.detail),
        error_code="HTTP_EXCEPTION"
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle request validation errors with detailed field information.
    
    Args:
        request: FastAPI request object
        exc: RequestValidationError instance
        
    Returns:
        JSONResponse: Standardized validation error response
    """
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(loc) for loc in error.get("loc", [])),
            "message": error.get("msg", ""),
            "type": error.get("type", ""),
            "input": error.get("input")
        })
    
    logger.warning(
        f"Validation error: {len(errors)} field(s) failed validation",
        extra={
            "errors": errors,
            "path": request.url.path,
            "method": request.method,
            "request_id": getattr(request.state, "request_id", None)
        }
    )
    
    return create_error_response(
        request=request,
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="Request validation failed",
        error_code="VALIDATION_ERROR",
        details={"validation_errors": errors}
    )


async def mandy_exception_handler(request: Request, exc: MandyException) -> JSONResponse:
    """
    Handle custom Mandy exceptions.
    
    Args:
        request: FastAPI request object
        exc: MandyException instance
        
    Returns:
        JSONResponse: Standardized error response
    """
    logger.error(
        f"Mandy exception: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method,
            "request_id": getattr(request.state, "request_id", None)
        }
    )
    
    return create_error_response(
        request=request,
        status_code=exc.status_code,
        message=exc.message,
        error_code=exc.error_code,
        details=exc.details
    )


async def rate_limit_exception_handler(request: Request, exc: RateLimitExceeded) -> JSONResponse:
    """
    Handle rate limit exceeded errors.
    
    Args:
        request: FastAPI request object
        exc: RateLimitExceeded instance
        
    Returns:
        JSONResponse: Standardized rate limit error response
    """
    logger.warning(
        f"Rate limit exceeded: {exc.detail}",
        extra={
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else "unknown",
            "request_id": getattr(request.state, "request_id", None)
        }
    )
    
    return create_error_response(
        request=request,
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        message="Rate limit exceeded. Please try again later.",
        error_code="RATE_LIMIT_EXCEEDED",
        details={
            "limit": exc.detail,
            "retry_after": getattr(exc, 'retry_after', 60)
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle unexpected exceptions with proper logging and safe error responses.
    
    Args:
        request: FastAPI request object
        exc: Exception instance
        
    Returns:
        JSONResponse: Standardized error response
    """
    logger.error(
        f"Unexpected exception: {type(exc).__name__} - {str(exc)}",
        extra={
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "path": request.url.path,
            "method": request.method,
            "request_id": getattr(request.state, "request_id", None)
        },
        exc_info=True
    )
    
    # Don't expose internal error details in production
    is_debug = logger._core.min_level <= 10  # DEBUG level
    
    return create_error_response(
        request=request,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred" if not is_debug else str(exc),
        error_code="INTERNAL_SERVER_ERROR",
        details={"exception_type": type(exc).__name__} if is_debug else None,
        include_traceback=is_debug
    )
