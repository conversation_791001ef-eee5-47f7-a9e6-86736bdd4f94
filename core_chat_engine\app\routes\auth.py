"""
Authentication routes for user login and token management.

This module provides secure JWT-based authentication for the Mandy AI companion.
"""

import os
from datetime import datetime, timed<PERSON>ta
from typing import Optional

import jwt
from fastapi import APIRouter, HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger
from passlib.context import Crypt<PERSON>ontext

from app.models.auth import LoginRequest, LoginResponse, User
from app.utils.rate_limiting import limiter, auth_rate_limit
from app.utils.input_validation import ContentFilter


# Initialize router
router = APIRouter()

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Configuration
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY environment variable is required")

ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# Demo credentials from environment (for demo only)
DEMO_USERNAME = os.getenv("DEMO_USERNAME", "user")
DEMO_PASSWORD = os.getenv("DEMO_PASSWORD", "password")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except jwt.PyJWTError:
        return None


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Get current user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    payload = verify_token(credentials.credentials)
    if payload is None:
        raise credentials_exception

    username: str = payload.get("sub")
    if username is None:
        raise credentials_exception

    return username


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="User login",
    description="Authenticate user and return JWT access token",
)
@auth_rate_limit()
async def login(request: LoginRequest, http_request: Request):
    """
    Authenticate user and return JWT access token.

    Args:
        request: Login request with username and password
        http_request: FastAPI request object for rate limiting

    Returns:
        LoginResponse: Response with JWT token and user information
    """
    try:
        # Validate and sanitize input
        username = ContentFilter.validate_username(request.username)
        password = ContentFilter.validate_password(request.password)

        # Demo authentication (replace with proper user service in production)
        if username == DEMO_USERNAME and password == DEMO_PASSWORD:
            # Create JWT token
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": username},
                expires_delta=access_token_expires
            )

            # Create demo user
            user = User(
                user_id="demo_user_123",
                username=username,
                email="<EMAIL>",
                full_name="Demo User",
                is_active=True,
                created_at=datetime.utcnow(),
                trust_level=0.5,
                conversation_count=0
            )

            logger.info(f"User logged in: {username}")

            return LoginResponse(
                success=True,
                message="Login successful",
                access_token=access_token,
                token_type="bearer",
                expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                user=user
            )
        else:
            logger.warning(f"Failed login attempt for username: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed",
        )


@router.get(
    "/status",
    summary="Authentication status",
    description="Check authentication service status",
)
async def auth_status():
    """
    Check the status of the authentication service.

    Returns:
        Dict with authentication service status
    """
    return {
        "status": "ready",
        "auth_type": "demo",
        "demo_credentials": {
            "username": DEMO_USERNAME,
            "note": "This is a demo authentication system"
        },
        "timestamp": datetime.utcnow().isoformat()
    }
