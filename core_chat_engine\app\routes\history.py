"""
Conversation history routes for retrieving past interactions.

This module provides endpoints for accessing conversation history,
searching through past messages, and managing conversation metadata.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from shared.models.base import User, Conversation, Message, APIResponse
from shared.utils.logging import get_logger

from app.routes.auth import get_current_user
from app.services.graphiti_service import GraphitiService, get_graphiti_service
from app.utils.rate_limiting import api_rate_limit
from app.utils.caching import cache_chat_history


# Initialize router
router = APIRouter()

# Logger
logger = get_logger("history")


# Request/Response models
class ConversationSummary(BaseModel):
    """Summary of a conversation."""
    id: UUID
    title: Optional[str]
    message_count: int
    last_message_at: datetime
    emotional_summary: List[str] = Field(default_factory=list)
    topics: List[str] = Field(default_factory=list)
    trust_score_change: float = 0.0


class HistorySearchRequest(BaseModel):
    """Search request for conversation history."""
    query: str = Field(..., min_length=1, max_length=200)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    emotion_filter: Optional[List[str]] = None
    limit: int = Field(default=20, ge=1, le=100)


@router.get(
    "/conversations",
    response_model=APIResponse,
    summary="Get conversation list",
    description="Retrieve a list of user's conversations with summaries",
)
@api_rate_limit()
@cache_chat_history(expire=300)  # Cache for 5 minutes
async def get_conversations(
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    limit: int = Query(default=20, ge=1, le=100),
    offset: int = Query(default=0, ge=0),
    date_from: Optional[datetime] = Query(None),
    date_to: Optional[datetime] = Query(None),
):
    """
    Get a list of user's conversations with summaries.
    
    Args:
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
        limit: Maximum number of conversations to return
        offset: Number of conversations to skip
        date_from: Filter conversations from this date
        date_to: Filter conversations to this date
    
    Returns:
        APIResponse: List of conversation summaries
    """
    try:
        # Retrieve conversations from Graphiti
        conversations = await graphiti_service.get_user_conversations(
            user_id=current_user.id,
            group_id=current_user.group_id,
            limit=limit,
            offset=offset,
            date_from=date_from,
            date_to=date_to
        )
        
        # Convert to summary format
        conversation_summaries = []
        for conv in conversations:
            summary = ConversationSummary(
                id=conv.get("id"),
                title=conv.get("title"),
                message_count=conv.get("message_count", 0),
                last_message_at=conv.get("last_message_at"),
                emotional_summary=conv.get("emotional_summary", []),
                topics=conv.get("topics", []),
                trust_score_change=conv.get("trust_score_change", 0.0)
            )
            conversation_summaries.append(summary)
        
        logger.info(
            "Retrieved conversation history",
            user_id=str(current_user.id),
            conversation_count=len(conversation_summaries),
            limit=limit,
            offset=offset
        )
        
        return APIResponse(
            success=True,
            message="Conversations retrieved successfully",
            data={
                "conversations": [conv.dict() for conv in conversation_summaries],
                "total_count": len(conversation_summaries),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to retrieve conversations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations"
        )


@router.get(
    "/conversations/{conversation_id}",
    response_model=APIResponse,
    summary="Get conversation details",
    description="Retrieve detailed information about a specific conversation",
)
async def get_conversation_details(
    conversation_id: UUID,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    include_messages: bool = Query(default=True),
    message_limit: int = Query(default=50, ge=1, le=200),
):
    """
    Get detailed information about a specific conversation.
    
    Args:
        conversation_id: ID of the conversation
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
        include_messages: Whether to include message history
        message_limit: Maximum number of messages to return
    
    Returns:
        APIResponse: Detailed conversation information
    """
    try:
        # Retrieve conversation details
        conversation = await graphiti_service.get_conversation_details(
            conversation_id=conversation_id,
            user_id=current_user.id,
            group_id=current_user.group_id,
            include_messages=include_messages,
            message_limit=message_limit
        )
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        logger.info(
            "Retrieved conversation details",
            user_id=str(current_user.id),
            conversation_id=str(conversation_id),
            include_messages=include_messages
        )
        
        return APIResponse(
            success=True,
            message="Conversation details retrieved successfully",
            data=conversation
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve conversation details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation details"
        )


@router.post(
    "/search",
    response_model=APIResponse,
    summary="Search conversation history",
    description="Search through conversation history using semantic and keyword search",
)
async def search_history(
    request: HistorySearchRequest,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Search through conversation history.
    
    This endpoint allows users to search through their conversation history
    using semantic search, keyword matching, and various filters.
    
    Args:
        request: Search request parameters
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Search results with matching conversations and messages
    """
    try:
        # Perform search using Graphiti
        search_results = await graphiti_service.search_conversation_history(
            query=request.query,
            user_id=current_user.id,
            group_id=current_user.group_id,
            date_from=request.date_from,
            date_to=request.date_to,
            emotion_filter=request.emotion_filter,
            limit=request.limit
        )
        
        logger.info(
            "Performed history search",
            user_id=str(current_user.id),
            query=request.query,
            results_count=len(search_results),
            date_from=request.date_from,
            date_to=request.date_to
        )
        
        return APIResponse(
            success=True,
            message="Search completed successfully",
            data={
                "results": search_results,
                "query": request.query,
                "total_results": len(search_results),
                "search_metadata": {
                    "date_from": request.date_from,
                    "date_to": request.date_to,
                    "emotion_filter": request.emotion_filter
                }
            }
        )
        
    except Exception as e:
        logger.error(f"History search failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )


@router.get(
    "/summary",
    response_model=APIResponse,
    summary="Get conversation summary",
    description="Get a summary of user's conversation patterns and statistics",
)
async def get_conversation_summary(
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    days: int = Query(default=30, ge=1, le=365),
):
    """
    Get a summary of user's conversation patterns and statistics.
    
    Args:
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
        days: Number of days to include in summary
    
    Returns:
        APIResponse: Conversation summary and statistics
    """
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get conversation statistics
        stats = await graphiti_service.get_conversation_statistics(
            user_id=current_user.id,
            group_id=current_user.group_id,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(
            "Generated conversation summary",
            user_id=str(current_user.id),
            days=days,
            total_conversations=stats.get("total_conversations", 0)
        )
        
        return APIResponse(
            success=True,
            message="Conversation summary generated successfully",
            data={
                "period": {
                    "days": days,
                    "start_date": start_date,
                    "end_date": end_date
                },
                "statistics": stats
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate conversation summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate summary"
        )


@router.delete(
    "/conversations/{conversation_id}",
    response_model=APIResponse,
    summary="Delete conversation",
    description="Delete a specific conversation and its associated data",
)
async def delete_conversation(
    conversation_id: UUID,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Delete a specific conversation and its associated data.
    
    Args:
        conversation_id: ID of the conversation to delete
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Deletion confirmation
    """
    try:
        # Verify conversation ownership
        conversation = await graphiti_service.get_conversation_details(
            conversation_id=conversation_id,
            user_id=current_user.id,
            group_id=current_user.group_id,
            include_messages=False
        )
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Delete conversation
        await graphiti_service.delete_conversation(
            conversation_id=conversation_id,
            user_id=current_user.id,
            group_id=current_user.group_id
        )
        
        logger.info(
            "Deleted conversation",
            user_id=str(current_user.id),
            conversation_id=str(conversation_id)
        )
        
        return APIResponse(
            success=True,
            message="Conversation deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )
