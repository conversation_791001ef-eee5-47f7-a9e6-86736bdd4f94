"""
Mandy Core Chat Engine - Main FastAPI Application

This is the core conversational AI service for <PERSON>, an emotionally intelligent
AI companion. It orchestrates the cognitive loop between Gemini AI and Graphiti
memory framework.
"""

import os
import time
import uuid
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
from dotenv import load_dotenv
from starlette_prometheus import PrometheusMiddleware, handle_metrics

# Load environment variables
load_dotenv()

# Import routes
from app.routes import auth, chat, health, history, memory
from app.services.graphiti_service import GraphitiService
from app.utils.rate_limiting import limiter, rate_limit_exceeded_handler
from app.utils.error_handlers import (
    http_exception_handler,
    validation_exception_handler,
    mandy_exception_handler,
    rate_limit_exception_handler,
    general_exception_handler,
    MandyException
)
from app.utils.caching import init_cache


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle events.
    """
    # Startup: Initialize services
    logger.info("Starting Mandy Core Chat Engine...")

    # Initialize caching system
    try:
        init_cache()
        logger.info("Cache system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize cache system: {e}")
        # Don't raise - caching is not critical for basic functionality

    # Initialize Graphiti service
    try:
        graphiti_service = GraphitiService()
        await graphiti_service.initialize()
        app.state.graphiti_service = graphiti_service
        logger.info("Graphiti service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Graphiti service: {e}")
        raise

    logger.info("Mandy Core Chat Engine started successfully")
    yield

    # Shutdown: Clean up resources
    logger.info("Shutting down Mandy Core Chat Engine...")


# Create FastAPI application
app = FastAPI(
    title="Mandy Core Chat Engine",
    description="Core conversational AI service for Mandy, an emotionally intelligent AI companion",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("DEBUG", "false").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "false").lower() == "true" else None,
)


# Add CORS middleware with secure configuration
cors_origins = os.getenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:7860")
if cors_origins:
    allowed_origins = [origin.strip() for origin in cors_origins.split(",")]
else:
    allowed_origins = []

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["Authorization", "Content-Type"],
)

# Add Prometheus metrics middleware
app.add_middleware(PrometheusMiddleware)
app.add_route("/metrics", handle_metrics)

# Add rate limiting
app.state.limiter = limiter

# Add comprehensive error handling
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(422, validation_exception_handler)
app.add_exception_handler(MandyException, mandy_exception_handler)
app.add_exception_handler(429, rate_limit_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)


# Request middleware for logging and timing
@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """
    Process each request for logging, timing, and error handling.
    """
    # Generate request ID
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id

    # Start timer
    start_time = time.time()

    try:
        # Process the request
        response = await call_next(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log successful request
        logger.info(
            f"Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "duration_ms": round(duration_ms, 2),
            }
        )

        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log failed request
        logger.error(
            f"Request failed: {str(e)}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "duration_ms": round(duration_ms, 2),
                "error": str(e),
            }
        )

        # Return error response
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "Internal server error",
                "request_id": request_id,
            },
            headers={"X-Request-ID": request_id}
        )


# Include routers
app.include_router(health.router, tags=["Health"])
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(chat.router, prefix="/chat", tags=["Chat"])
app.include_router(history.router, prefix="/history", tags=["History"])
app.include_router(memory.router, prefix="/memory", tags=["Memory"])


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint that returns API information."""
    return {
        "name": "Mandy Core Chat Engine",
        "version": "1.0.0",
        "status": "online",
        "description": "Emotionally intelligent AI companion core service"
    }


# Error handlers are now centralized in app.utils.error_handlers


if __name__ == "__main__":
    import uvicorn

    # Get configuration from environment
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    workers = int(os.getenv("API_WORKERS", "1"))
    debug = os.getenv("DEBUG", "false").lower() == "true"

    # Run the application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=workers if not debug else 1,
        reload=debug,
        log_level="debug" if debug else "info",
    )
