#!/usr/bin/env python3
"""
Simple API test script for Mandy AI Companion.

This script tests the basic functionality of the Core Chat Engine API
to ensure everything is working correctly.
"""

import asyncio
import json
import sys
from datetime import datetime
from uuid import uuid4

import httpx


class MandyAPITester:
    """Simple API tester for Mandy AI Companion."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the API tester."""
        self.base_url = base_url
        self.access_token = None
        self.conversation_id = None
    
    async def test_health_check(self):
        """Test the health check endpoint."""
        print("🔍 Testing health check...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Health check passed: {data['status']}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Health check error: {e}")
                return False
    
    async def test_login(self):
        """Test user authentication."""
        print("🔐 Testing login...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/auth/login",
                    json={
                        "username": "admin",
                        "password": "admin123"
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        self.access_token = data["data"]["access_token"]
                        print(f"✅ Login successful: {data['data']['user']['username']}")
                        return True
                    else:
                        print(f"❌ Login failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Login failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Login error: {e}")
                return False
    
    async def test_get_current_user(self):
        """Test getting current user information."""
        print("👤 Testing get current user...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    f"{self.base_url}/auth/me",
                    headers={"Authorization": f"Bearer {self.access_token}"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        user_info = data["data"]
                        print(f"✅ User info retrieved: {user_info['username']} (Trust: {user_info['trust_score']})")
                        return True
                    else:
                        print(f"❌ Get user failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Get user failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Get user error: {e}")
                return False
    
    async def test_send_message(self, message: str):
        """Test sending a chat message."""
        print(f"💬 Testing chat message: '{message}'...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Generate conversation ID if not exists
                if not self.conversation_id:
                    self.conversation_id = str(uuid4())
                
                response = await client.post(
                    f"{self.base_url}/chat/",
                    headers={"Authorization": f"Bearer {self.access_token}"},
                    json={
                        "message": message,
                        "user_id": "test_user",
                        "conversation_id": self.conversation_id,
                        "context": {"test": True}
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        response_text = data["response_text"]
                        message_bursts = data["message_bursts"]

                        print(f"✅ Chat response received:")
                        print(f"   📝 Response: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                        print(f"   💭 Message bursts: {len(message_bursts)}")
                        print(f"   🧠 Memory used: {data.get('memory_context_used', False)}")

                        return True
                    else:
                        print(f"❌ Chat failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Chat failed: {response.status_code}")
                    if response.status_code == 422:
                        print(f"   Validation error: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ Chat error: {e}")
                return False
    
    async def test_memory_stats(self):
        """Test memory statistics endpoint."""
        print("🧠 Testing memory stats...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    f"{self.base_url}/memory/stats",
                    headers={"Authorization": f"Bearer {self.access_token}"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        stats = data["data"]
                        print(f"✅ Memory stats retrieved:")
                        print(f"   📝 Episodes: {stats.get('total_episodes', 0)}")
                        print(f"   🔗 Nodes: {stats.get('total_nodes', 0)}")
                        print(f"   ↔️  Edges: {stats.get('total_edges', 0)}")
                        return True
                    else:
                        print(f"❌ Memory stats failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Memory stats failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Memory stats error: {e}")
                return False
    
    async def run_all_tests(self):
        """Run all API tests."""
        print("🤖 Starting Mandy API Tests")
        print("=" * 50)
        
        tests = [
            ("Health Check", self.test_health_check()),
            ("Login", self.test_login()),
            ("Get Current User", self.test_get_current_user()),
            ("Send Happy Message", self.test_send_message("Hello Mandy! I'm feeling really happy today!")),
            ("Send Sad Message", self.test_send_message("I'm feeling a bit sad and need someone to talk to.")),
            ("Send Neutral Message", self.test_send_message("Can you tell me about yourself?")),
            ("Memory Stats", self.test_memory_stats()),
        ]
        
        results = []
        
        for test_name, test_coro in tests:
            print(f"\n🧪 Running: {test_name}")
            try:
                result = await test_coro
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ Test '{test_name}' crashed: {e}")
                results.append((test_name, False))
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if result:
                passed += 1
            else:
                failed += 1
        
        print(f"\n📈 Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All tests passed! Mandy is ready to chat!")
        else:
            print("⚠️  Some tests failed. Check the logs above for details.")
        
        return failed == 0


async def main():
    """Main function to run the API tests."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print(f"🎯 Testing Mandy API at: {base_url}")
    
    tester = MandyAPITester(base_url)
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 Mandy is ready for action!")
        sys.exit(0)
    else:
        print("\n🔧 Please check the Core Chat Engine and try again.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
